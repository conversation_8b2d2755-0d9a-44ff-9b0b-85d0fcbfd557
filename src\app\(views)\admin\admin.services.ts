import axiosInstance from '@/lib/axios';
import { useAuthStore } from '@/store';

export const fetchUsers = async () => {
  const response = await axiosInstance.get(`/admin/users`);
  return response.data;
};

export const fetchUser = async (userId: string) => {
  const response = await axiosInstance.get(`/admin/users/${userId}`);
  return response.data;
};

export const fetchWarehouses = async () => {
  const user = useAuthStore.getState().user;
  
  // If user is not available yet, return an empty array instead of rejecting
  // This prevents cascading session calls when components try to fetch warehouses
  // before authentication is complete
  if (!user?.Email) {
    return [];
  }

  const response = await axiosInstance.get('/admin/warehouses');
  return response.data;
};

export const createUser = async (userData: {
  Name: string;
  Email: string;
  Role: string;
}) => {
  const response = await axiosInstance.post('/admin/users', userData);
  return response.data;
};

export const updateUser = async (userId: string, data: any) => {
  const response = await axiosInstance.put(`/admin/users/${userId}`, data);
  return response.data;
};

export const createWarehouse = async (data: { Name: string }) => {
  const response = await axiosInstance.post('/admin/warehouses', data);
  return response.data;
};

export const updateWarehouse = async (
  warehouseId: string,
  data: { Name: string }
) => {
  const response = await axiosInstance.put(
    `/admin/warehouses/${warehouseId}`,
    data
  );
  return response.data;
};

export const deleteWarehouse = async (warehouseId: string) => {
  const response = await axiosInstance.delete(
    `/admin/warehouses/${warehouseId}`
  );
  return response.data;
};

export const fetchVessels = async () => {
  const response = await axiosInstance.get('/vessels');
  return response.data;
};

export const createVessel = async (data: { Name: string }) => {
  const response = await axiosInstance.post('/vessels', data);
  return response.data;
};

export async function updateVessel(vesselId: string, data: any) {
  return axiosInstance.put(`/vessels/${vesselId}`, data);
}

export const fetchAssets = async () => {
  const response = await axiosInstance.get('/assets');
  return response.data;
};

export const createAsset = async (data: { Name: string }) => {
  const response = await axiosInstance.post('/assets', data);
  return response.data;
};

export const updateAsset = async (assetId: string, data: any) => {
  const response = await axiosInstance.put(`/assets/${assetId}`, data);
  return response.data;
};

export const fetchVendors = async () => {
  const response = await axiosInstance.get('/vendors');
  return response.data;
};

export const createVendor = async (data: { Name: string }) => {
  const response = await axiosInstance.post('/vendors', data);
  return response.data;
};

export const updateVendor = async (vendorId: string, data: any) => {
  const response = await axiosInstance.put(`/vendors/${vendorId}`, data);
  return response.data;
};

export const fetchUserWarehouseAssignments = async (userId: string) => {
  // If userId is 'all', fetch all assignments, otherwise fetch for specific user
  const endpoint = userId === 'all' ? '/admin/user-warehouse' : `/admin/user-warehouse/${userId}`;
  const response = await axiosInstance.get(endpoint);
  return response.data;
};

export const assignUserToWarehouse = async (assignmentData: {
  UserId: string;
  WarehouseId: string;
}) => {
  const response = await axiosInstance.post(
    '/admin/user-warehouse',
    assignmentData
  );
  return response.data;
};

export const removeUserFromWarehouse = async (assignmentData: {
  UserId: string;
  WarehouseId: string;
}) => {
  const response = await axiosInstance.delete('/admin/user-warehouse', {
    data: assignmentData,
  });
  return response.data;
};

export const fetchActiveItemsCount = async () => {
  const response = await axiosInstance.get('/items/active-count');
  return response.data;
};

export const fetchQuarantinedItemsCount = async () => {
  const response = await axiosInstance.get('/items/quarantined-count');
  return response.data;
};

export const fetchMonthlyStats = async () => {
  const response = await axiosInstance.get('/items/stats');
  return Array.isArray(response.data.monthlyStats)
    ? response.data.monthlyStats
    : [];
};

// Asset-Warehouse relationship functions
export const fetchAssetWarehouses = async (assetId?: string) => {
  const endpoint = assetId 
    ? `/admin/asset-warehouse?assetId=${assetId}` 
    : '/admin/asset-warehouse';
  const response = await axiosInstance.get(endpoint);
  return response.data;
};

export const assignAssetToWarehouse = async (assignmentData: {
  AssetId: string;
  WarehouseId: string;
}) => {
  const response = await axiosInstance.post(
    '/admin/asset-warehouse',
    assignmentData
  );
  return response.data;
};

export const removeAssetFromWarehouse = async (assignmentData: {
  AssetId: string;
  WarehouseId: string;
}) => {
  const response = await axiosInstance.delete('/admin/asset-warehouse', {
    data: assignmentData,
  });
  return response.data;
};

// Vessel-Warehouse relationship functions
export const fetchVesselWarehouses = async (vesselId?: string) => {
  const endpoint = vesselId 
    ? `/admin/vessel-warehouse?vesselId=${vesselId}` 
    : '/admin/vessel-warehouse';
  const response = await axiosInstance.get(endpoint);
  return response.data;
};

export const assignVesselToWarehouse = async (assignmentData: {
  VesselId: string;
  WarehouseId: string;
}) => {
  const response = await axiosInstance.post(
    '/admin/vessel-warehouse',
    assignmentData
  );
  return response.data;
};

export const removeVesselFromWarehouse = async (assignmentData: {
  VesselId: string;
  WarehouseId: string;
}) => {
  const response = await axiosInstance.delete('/admin/vessel-warehouse', {
    data: assignmentData,
  });
  return response.data;
};

// Vendor-Warehouse relationship functions
export const fetchVendorWarehouses = async (vendorId?: string) => {
  const endpoint = vendorId 
    ? `/admin/vendor-warehouse?vendorId=${vendorId}` 
    : '/admin/vendor-warehouse';
  const response = await axiosInstance.get(endpoint);
  return response.data;
};

export const assignVendorToWarehouse = async (assignmentData: {
  VendorId: string;
  WarehouseId: string;
}) => {
  const response = await axiosInstance.post(
    '/admin/vendor-warehouse',
    assignmentData
  );
  return response.data;
};

export const removeVendorFromWarehouse = async (assignmentData: {
  VendorId: string;
  WarehouseId: string;
}) => {
  const response = await axiosInstance.delete('/admin/vendor-warehouse', {
    data: assignmentData,
  });
  return response.data;
};
