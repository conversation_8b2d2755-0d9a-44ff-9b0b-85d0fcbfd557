@import 'tailwindcss';

:root {
  /* Dark theme variables (default) */
  --comet-accent: #e29151;
  --comet-background: #111111;
  --comet-card: #1a1a1a;
  --comet-card-dark: rgba(0, 0, 0, 0.3);
  --comet-border: #2a2a2a;
  --comet-text: #ffffff;
  --comet-text-secondary: #9ca3af;
  --header-bg: rgba(0, 0, 0, 0.3);

  --peterson-header: #1c1e41;
  --peterson-button: #08162a;
  --peterson-button-hover: #08164f;
  --peterson-primary: #d6002a;
  --peterson-primary-hover: #ad030f;
}

html.dark {
  background-color: var(--comet-background);
  color: var(--comet-text);
}

[data-theme='light'],
html:not(.dark) {
  --comet-background: #f8fafc;
  --comet-card: #ffffff;
  --comet-card-dark: rgba(0, 0, 0, 0.03);
  --comet-border: #e5e7eb;
  --comet-text: #111827;
  --comet-text-secondary: #6b7280;
  --header-bg: rgba(255, 255, 255, 0.8);
}

body {
  background-color: var(--comet-background);
  color: var(--comet-text);
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

header {
  background-color: var(--peterson-header);
  border-bottom: 1px solid var(--comet-border);
  color: white;
}

.card {
  background-color: var(--comet-card);
  border: 1px solid var(--comet-border);
  color: var(--comet-text);
}

.p-row-editor-init.disabled {
  opacity: 0.5;
  pointer-events: none;
  cursor: not-allowed;
}
