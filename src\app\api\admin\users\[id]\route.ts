import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';
import { getToken } from 'next-auth/jwt';
import { Role } from '@/app/shared/models/global.enums';

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await context.params;

    const user = await prisma.user.findUnique({
      where: { UserId: id },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    return NextResponse.json(user);
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const { id } = await context.params;
  try {
    // Get user information from JWT token
    const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });

    // If no token, check cookies as fallback
    const cookieStore = await cookies();
    let userEmail: string | undefined;

    if (!token) {
      try {
        userEmail = cookieStore.get('userEmail')?.value;
      } catch (error) {
        // Removed console.error statement
      }
    } else {
      // Prefer token data if available
      userEmail = token.email as string;
    }

    if (!userEmail) {
      return NextResponse.json(
        { error: 'Unauthorized: User not authenticated' },
        { status: 401 }
      );
    }

    // Always fetch the user from the database to get the most up-to-date role
    const userRecord = await prisma.user.findUnique({
      where: { Email: userEmail },
    });

    if (!userRecord) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Use the DB role as the source of truth
    const requesterRole = userRecord.Role;

    // Get the user we're trying to update
    const targetUser = await prisma.user.findUnique({
      where: { UserId: id },
    });

    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const requestData = await req.json();
    const { Name, Role: newRole, _currentUserRole } = requestData;

    if (_currentUserRole && _currentUserRole !== requesterRole) {
      return NextResponse.json(
        { error: 'Role validation failed' },
        { status: 403 }
      );
    }

    // Check if the requester has permission to modify this user
    if (requesterRole !== Role.Admin && requesterRole !== Role.SystemAdmin) {
      return NextResponse.json(
        { error: 'Only administrators can modify users' },
        { status: 403 }
      );
    }

    // Admins cannot modify system_admin users
    if (requesterRole === Role.Admin && targetUser.Role === Role.SystemAdmin) {
      return NextResponse.json(
        { error: 'Regular administrators cannot modify system administrators' },
        { status: 403 }
      );
    }

    // Users cannot change their own role
    const isSelfUpdate = userRecord.UserId === id;

    if (isSelfUpdate && newRole && newRole !== userRecord.Role) {
      return NextResponse.json(
        { error: 'You cannot change your own role' },
        { status: 403 }
      );
    }

    // Role change restrictions
    if (newRole) {
      // Only system_admin can set or change to system_admin role
      if (newRole === Role.SystemAdmin && requesterRole !== Role.SystemAdmin) {
        return NextResponse.json(
          {
            error:
              'Only system administrators can assign the system_admin role',
          },
          { status: 403 }
        );
      }

      // Validate role is one of the allowed values
      if (!Object.values(Role).includes(newRole)) {
        return NextResponse.json(
          { error: 'Role must be one of system_admin, admin, read, or write' },
          { status: 400 }
        );
      }
    }
    const updatedUser = await prisma.user.update({
      where: { UserId: id },
      data: { Name, Role: newRole },
    });
    return NextResponse.json(updatedUser);
  } catch (error) {
    // Removed console.error statement
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
