'use client';

import useS<PERSON> from 'swr';
import { useRef, useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import ReusableToast from '@/app/shared/components/ReusableToast';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { textEditor } from '../../receipts/utils/editors.utils';
import { openDeleteConfirmDialog } from '../../receipts/components/utils/ReceiptsDialogsSettings';
import ReusableConfirmDialog from '@/app/shared/components/ReusableConfirmDialog';
import {
  fetchVessels,
  createVessel,
  updateVessel,
  fetchWarehouses,
  fetchVesselWarehouses,
  assignVesselToWarehouse,
  removeVesselFromWarehouse,
} from '../admin.services';
import { useAuthStore } from '@/store';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrash } from '@fortawesome/free-solid-svg-icons';
import ReusableDialog from '@/app/shared/components/ReusableDialog';

export default function VesselsManagement() {
  const {
    data: vessels,
    error,
    mutate,
  } = useSWR('/admin/vessels', fetchVessels);
  const [newVessel, setNewVessel] = useState({ Name: '' });
  const [editingRowId, setEditingRowId] = useState<string | null>(null);
  const toastRef = useRef<any>(null);
  const [assignWarehousesDialogVisible, setAssignWarehousesDialogVisible] =
    useState(false);
  const [vesselToAssign, setVesselToAssign] = useState<any>(null);

  // Fetch warehouses for the dialog
  const { data: warehouses } = useSWR('/admin/warehouses', fetchWarehouses);

  // Fetch vessel-warehouse assignments
  const { data: assignments, mutate: mutateAssignments } = useSWR(
    vesselToAssign
      ? `/admin/vessel-warehouse?vesselId=${vesselToAssign.VesselId}`
      : null,
    () => fetchVesselWarehouses(vesselToAssign?.VesselId)
  );

  const isUserLoading = useAuthStore((state) => state.isUserLoading);
  if (isUserLoading) return <p>Loading vessels...</p>;

  const onRowEditInit = (e: any) => setEditingRowId(e.data.VesselId);
  const onRowEditCancel = () => setEditingRowId(null);

  const onRowEditComplete = async (e: any) => {
    try {
      await updateVessel(e.newData.VesselId, { Name: e.newData.Name });
      toastRef.current?.show({
        severity: 'success',
        summary: 'Updated',
        detail: 'Vessel updated',
      });
      mutate();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to update vessel',
      });
    } finally {
      setEditingRowId(null);
    }
  };

  const handleCreate = async () => {
    try {
      const newCreatedVessel = await createVessel(newVessel);
      setNewVessel({ Name: '' });
      toastRef.current?.show({
        severity: 'success',
        summary: 'Created',
        detail: 'Vessel created',
      });
      await mutate();

      // Automatically open the warehouse assignment dialog for the new vessel
      if (newCreatedVessel) {
        setVesselToAssign(newCreatedVessel);
        setAssignWarehousesDialogVisible(true);
        mutateAssignments();
      }
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to create vessel',
      });
    }
  };

  const handleDelete = async (v: any) => {
    try {
      await updateVessel(v.VesselId, { IsDeleted: true }); // soft-delete
      toastRef.current?.show({
        severity: 'success',
        summary: 'Deleted',
        detail: 'Vessel deleted',
      });
      mutate();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to delete vessel',
      });
    }
  };

  const confirmDelete = (rowData: any) => {
    openDeleteConfirmDialog({ onAccept: () => handleDelete(rowData) });
  };

  const openAssignWarehousesDialog = (rowData: any) => {
    setVesselToAssign(rowData);
    setAssignWarehousesDialogVisible(true);
    mutateAssignments();
  };

  const isWarehouseAssigned = (warehouseId: string) => {
    return assignments?.some(
      (a: any) =>
        a.WarehouseId === warehouseId && a.VesselId === vesselToAssign?.VesselId
    );
  };

  const handleAssignWarehouse = async (warehouseId: string) => {
    if (!vesselToAssign) return;
    try {
      await assignVesselToWarehouse({
        VesselId: vesselToAssign.VesselId,
        WarehouseId: warehouseId,
      });
      mutateAssignments();
      toastRef.current?.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Vessel assigned to warehouse',
      });
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to assign vessel to warehouse',
      });
    }
  };

  const areAllWarehousesAssigned = () => {
    if (!warehouses || !assignments) return false;
    return warehouses.every((warehouse: any) =>
      isWarehouseAssigned(warehouse.WarehouseId)
    );
  };

  const handleAssignOrUnassignAll = async () => {
    if (!vesselToAssign || !warehouses) return;

    const allAssigned = areAllWarehousesAssigned();

    try {
      if (allAssigned) {
        // Unassign from all warehouses
        const unassignmentPromises = warehouses.map((warehouse: any) =>
          removeVesselFromWarehouse({
            VesselId: vesselToAssign.VesselId,
            WarehouseId: warehouse.WarehouseId,
          })
        );

        await Promise.all(unassignmentPromises);

        toastRef.current?.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Vessel unassigned from all warehouses',
        });
      } else {
        // Assign to all unassigned warehouses
        const assignmentPromises = warehouses
          .filter(
            (warehouse: any) => !isWarehouseAssigned(warehouse.WarehouseId)
          )
          .map((warehouse: any) =>
            assignVesselToWarehouse({
              VesselId: vesselToAssign.VesselId,
              WarehouseId: warehouse.WarehouseId,
            })
          );

        await Promise.all(assignmentPromises);

        toastRef.current?.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Vessel assigned to all warehouses',
        });
      }

      mutateAssignments();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: allAssigned
          ? 'Failed to unassign vessel from all warehouses'
          : 'Failed to assign vessel to all warehouses',
      });
    }
  };

  const handleRemoveWarehouse = async (warehouseId: string) => {
    if (!vesselToAssign) return;
    try {
      await removeVesselFromWarehouse({
        VesselId: vesselToAssign.VesselId,
        WarehouseId: warehouseId,
      });
      mutateAssignments();
      toastRef.current?.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Vessel removed from warehouse',
      });
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to remove vessel from warehouse',
      });
    }
  };

  if (!vessels) return <p>Loading...</p>;
  if (error) return <p>Error loading vessels</p>;

  return (
    <>
      <ReusableToast ref={toastRef} />
      <h1>Vessels Management</h1>
      <div className="flex gap-2 my-2">
        <InputText
          placeholder="Name"
          value={newVessel.Name}
          onChange={(e) => setNewVessel({ Name: e.target.value })}
        />
        <Button
          disabled={!newVessel.Name}
          label="Create Vessel"
          onClick={handleCreate}
        />
      </div>

      <DataTable
        value={vessels}
        editMode="row"
        dataKey="VesselId"
        size="small"
        stripedRows
        onRowEditInit={onRowEditInit}
        onRowEditCancel={onRowEditCancel}
        onRowEditComplete={onRowEditComplete}
      >
        {/* <Column field="VesselId" header="ID" /> */}
        <Column
          field="Name"
          header="Name"
          editor={(opts) => textEditor(opts)}
        />
        <Column rowEditor headerStyle={{ width: '4rem' }} />
        <Column
          body={(rowData) => (
            <Button
              icon={<FontAwesomeIcon icon={faTrash} />}
              className="p-button-text p-button-rounded p-button-danger"
              onClick={() => confirmDelete(rowData)}
              tooltip="Delete"
              tooltipOptions={{ position: 'top' }}
            />
          )}
          headerStyle={{ width: '6rem' }}
        />
        <Column
          body={(rowData) => (
            <Button
              label="Warehouses"
              onClick={() => openAssignWarehousesDialog(rowData)}
              className="p-button-sm"
              tooltip="Assign Warehouses"
              tooltipOptions={{ position: 'top' }}
            />
          )}
          headerStyle={{ width: '6rem' }}
          bodyStyle={{ textAlign: 'center' }}
        />
      </DataTable>

      <ReusableConfirmDialog />

      <ReusableDialog
        visible={assignWarehousesDialogVisible}
        onHide={() => setAssignWarehousesDialogVisible(false)}
        header={`Assign Warehouses to ${vesselToAssign?.Name || ''}`}
      >
        {warehouses && assignments ? (
          <div>
            <div className="flex justify-end mb-3">
              <Button
                label={
                  areAllWarehousesAssigned()
                    ? 'Unassign from All Warehouses'
                    : 'Assign to All Warehouses'
                }
                onClick={handleAssignOrUnassignAll}
                className={
                  areAllWarehousesAssigned()
                    ? 'p-button-sm p-button-danger'
                    : 'p-button-sm p-button-success'
                }
              />
            </div>
            {warehouses.map((warehouse: any) => (
              <div
                key={warehouse.WarehouseId}
                className="flex items-center justify-between mb-2"
              >
                <span>{warehouse.Name}</span>
                {isWarehouseAssigned(warehouse.WarehouseId) ? (
                  <Button
                    label="Remove"
                    onClick={() => handleRemoveWarehouse(warehouse.WarehouseId)}
                    className="p-button-secondary"
                  />
                ) : (
                  <Button
                    label="Assign"
                    onClick={() => handleAssignWarehouse(warehouse.WarehouseId)}
                  />
                )}
              </div>
            ))}
          </div>
        ) : (
          <p>Loading warehouses...</p>
        )}
      </ReusableDialog>
    </>
  );
}
