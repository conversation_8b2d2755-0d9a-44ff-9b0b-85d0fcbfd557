import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  const url = new URL(request.url);
  const pathname = url.pathname;
  const pathParts = pathname.split('/');
  const lastPathSegment = pathParts[pathParts.length - 1];

  if (lastPathSegment === 'assets') {
    try {
      const warehouseId = url.searchParams.get('warehouseId');

      if (warehouseId && warehouseId !== 'all') {
        // Get assets that have a relationship with this warehouse
        const assetsWithWarehouseRelation =
          await prisma.assetWarehouse.findMany({
            where: { WarehouseId: warehouseId },
            select: { AssetId: true },
          });

        const assetIds = assetsWithWarehouseRelation.map(
          (relation) => relation.AssetId
        );

        const assets = await prisma.asset.findMany({
          where: {
            IsDeleted: false,
            AssetId: { in: assetIds },
          },
          orderBy: { Name: 'asc' },
        });

        return NextResponse.json(assets);
      } else {
        const assets = await prisma.asset.findMany({
          where: { IsDeleted: false },
          orderBy: { Name: 'asc' },
        });
        return NextResponse.json(assets);
      }
    } catch (error) {
      return NextResponse.json(
        { error: 'Error listing assets' },
        { status: 500 }
      );
    }
  } else {
    try {
      const asset = await prisma.asset.findUnique({
        where: { AssetId: lastPathSegment, IsDeleted: false },
      });
      if (!asset) {
        return NextResponse.json({ error: 'Not found' }, { status: 404 });
      }
      return NextResponse.json(asset);
    } catch (error) {
      return NextResponse.json(
        { error: 'Error fetching asset' },
        { status: 500 }
      );
    }
  }
}

export async function POST(request: NextRequest) {
  try {
    const { Name } = await request.json();
    const newAsset = await prisma.asset.create({ data: { Name } });
    return NextResponse.json(newAsset, { status: 201 });
  } catch (error) {
    return NextResponse.json(
      { error: 'Error creating asset' },
      { status: 500 }
    );
  }
}
