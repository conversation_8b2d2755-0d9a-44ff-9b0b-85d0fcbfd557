import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';
import { getToken } from 'next-auth/jwt';
import {
  parsePackageTypes,
  stringifyPackageTypes,
} from '@/app/shared/utils/packageTypes.utils';
import { Role } from '@/app/shared/models/global.enums';

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await context.params;

    const item = await prisma.item.findUnique({
      where: { ItemId: id },
      include: {
        Warehouse: true,
        ReceivedUser: true,
        Asset: true,
        Vendor: true,
        Vessel: true,
      },
    });

    if (!item) {
      return NextResponse.json({ error: 'Item not found' }, { status: 404 });
    }

    // Parse packageType JSON string to array
    const sanitizedItem = {
      ...item,
      packageType: parsePackageTypes(item.packageType),
    };

    return NextResponse.json(sanitizedItem);
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // Get user information from JWT token
    const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });

    // If no token, check cookies as fallback
    const cookieStore = await cookies();
    let userEmail: string | undefined;

    if (!token) {
      try {
        userEmail = cookieStore.get('userEmail')?.value;
      } catch (error) {}
    } else {
      // Prefer token data if available
      userEmail = token.email as string;
    }

    if (!userEmail) {
      return NextResponse.json(
        { error: 'Unauthorized: User not authenticated' },
        { status: 401 }
      );
    }

    // Always fetch the user from the database to get the most up-to-date role
    const userRecord = await prisma.user.findUnique({
      where: { Email: userEmail },
    });

    if (!userRecord) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Use the DB role as the source of truth
    const role = userRecord.Role;

    if (role === Role.Read) {
      return NextResponse.json(
        { error: 'Forbidden: read-only users cannot update items' },
        { status: 403 }
      );
    }

    if (
      role !== Role.Write &&
      role !== Role.Admin &&
      role !== Role.SystemAdmin
    ) {
      return NextResponse.json(
        { error: 'Forbidden: insufficient permissions to update items' },
        { status: 403 }
      );
    }

    const { id } = await context.params;
    const fullData = await req.json();
    // Remove identifier, relational objects, foreign key fields, not needed for editing
    const {
      ItemId,
      Warehouse,
      WarehouseId,
      ReceivedUser,
      ReceivedByUserId,
      Asset,
      // AssetId,
      Vendor,
      // VendorId,
      Vessel,
      // VesselId,
      ElapsedTime,
      ...updateData
    } = fullData;

    // Convert packageType array to JSON string if it exists
    if (updateData.packageType) {
      updateData.packageType = stringifyPackageTypes(updateData.packageType);
    }

    const updatedItem = await prisma.item.update({
      where: { ItemId: id },
      data: updateData,
      include: {
        Warehouse: true,
        ReceivedUser: true,
        Asset: true,
        Vendor: true,
        Vessel: true,
      },
    });

    // Parse packageType JSON string to array for response
    const sanitizedItem = {
      ...updatedItem,
      packageType: parsePackageTypes(updatedItem.packageType),
    };

    return NextResponse.json(sanitizedItem);
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
