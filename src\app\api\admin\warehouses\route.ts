import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { cookies } from 'next/headers';
import { getToken } from 'next-auth/jwt';
import { Role } from '@/app/shared/models/global.enums';

export async function GET(request: NextRequest) {
  try {
    // Get user information from JWT token
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    });

    // If no token, check cookies as fallback
    const cookieStore = await cookies();
    let userEmail: string | undefined;

    if (!token) {
      try {
        userEmail = cookieStore.get('userEmail')?.value;
      } catch (error) {
        console.error('Error accessing cookies:', error);
      }
    } else {
      // Prefer token data if available
      userEmail = token.email as string;
    }

    if (!userEmail) {
      return NextResponse.json(
        { error: 'Unauthorized: User not authenticated' },
        { status: 401 }
      );
    }

    // Always fetch the user from the database to get the most up-to-date role
    const userRecord = await prisma.user.findUnique({
      where: { Email: userEmail },
    });

    if (!userRecord) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Use the database role as the source of truth
    const isSystemAdmin = userRecord.Role === Role.SystemAdmin;
    const isAdmin = userRecord.Role === Role.Admin;

    // If user is system_admin, return all warehouses
    // If user is admin or other roles, return only warehouses assigned to user
    if (isSystemAdmin) {
      const warehouses = await prisma.warehouse.findMany({
        where: {
          IsDeleted: { equals: false },
        },
      });
      return NextResponse.json(warehouses, { status: 200 });
    }

    // For admin and other roles, return only warehouses assigned to the user
    const userWarehouses = await prisma.userWarehouse.findMany({
      where: { UserId: userRecord.UserId },
      select: { WarehouseId: true },
    });

    const warehouseIds = userWarehouses.map(
      (uw: { WarehouseId: string }) => uw.WarehouseId
    );

    const warehouses = await prisma.warehouse.findMany({
      where: {
        WarehouseId: { in: warehouseIds },
        IsDeleted: { equals: false },
      },
    });

    return NextResponse.json(warehouses, { status: 200 });
  } catch (error) {
    console.error('Error fetching warehouses:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST: create a new warehouse
export async function POST(request: NextRequest) {
  try {
    // Get user information from JWT token
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    });

    // If no token, check cookies as fallback
    const cookieStore = await cookies();
    let userEmail: string | undefined;

    if (!token) {
      try {
        userEmail = cookieStore.get('userEmail')?.value;
      } catch (error) {
        console.error('Error accessing cookies:', error);
      }
    } else {
      // Prefer token data if available
      userEmail = token.email as string;
    }

    if (!userEmail) {
      return NextResponse.json(
        { error: 'Unauthorized: User not authenticated' },
        { status: 401 }
      );
    }

    // Always fetch the user from the database to get the most up-to-date role
    const userRecord = await prisma.user.findUnique({
      where: { Email: userEmail },
    });

    if (!userRecord) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Use the DB role as the source of truth
    const isSystemAdmin = userRecord.Role === Role.SystemAdmin;
    const isAdmin = userRecord.Role === Role.Admin;

    if (!isSystemAdmin && !isAdmin) {
      return NextResponse.json(
        {
          error: `Only Admin or System Admin users can create warehouses. Your current role (${userRecord.Role}) does not have this permission.`,
        },
        { status: 403 }
      );
    }

    const { Name } = await request.json();

    const newWarehouse = await prisma.warehouse.create({
      data: {
        Name,
        IsDeleted: false,
      },
    });

    // If the user is an admin (not system_admin), automatically assign them to the warehouse
    if (isAdmin && !isSystemAdmin) {
      await prisma.userWarehouse.create({
        data: {
          UserId: userRecord.UserId,
          WarehouseId: newWarehouse.WarehouseId,
        },
      });
    }

    return NextResponse.json(newWarehouse, { status: 201 });
  } catch (error) {
    console.error('Error creating warehouse:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
