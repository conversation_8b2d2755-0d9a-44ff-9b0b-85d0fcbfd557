'use client';

import useS<PERSON> from 'swr';
import { useRef, useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import ReusableToast from '@/app/shared/components/ReusableToast';
import { But<PERSON> } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { textEditor } from '../../receipts/utils/editors.utils';
import { openDeleteConfirmDialog } from '../../receipts/components/utils/ReceiptsDialogsSettings';
import ReusableConfirmDialog from '@/app/shared/components/ReusableConfirmDialog';
import {
  fetchVendors,
  createVendor,
  updateVendor,
  fetchWarehouses,
  fetchVendorWarehouses,
  assignVendorToWarehouse,
  removeVendorFromWarehouse,
} from '../admin.services';
import { useAuthStore } from '@/store';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrash } from '@fortawesome/free-solid-svg-icons';
import ReusableDialog from '@/app/shared/components/ReusableDialog';

export default function VendorsManagement() {
  const {
    data: vendors,
    error,
    mutate,
  } = useSWR('/admin/vendors', fetchVendors);
  const [newVendor, setNewVendor] = useState({ Name: '' });
  const [editingRowId, setEditingRowId] = useState<string | null>(null);
  const toastRef = useRef<any>(null);
  const [assignWarehousesDialogVisible, setAssignWarehousesDialogVisible] =
    useState(false);
  const [vendorToAssign, setVendorToAssign] = useState<any>(null);

  // Fetch warehouses for the dialog
  const { data: warehouses } = useSWR('/admin/warehouses', fetchWarehouses);

  // Fetch vendor-warehouse assignments
  const { data: assignments, mutate: mutateAssignments } = useSWR(
    vendorToAssign
      ? `/admin/vendor-warehouse?vendorId=${vendorToAssign.VendorId}`
      : null,
    () => fetchVendorWarehouses(vendorToAssign?.VendorId)
  );

  const isUserLoading = useAuthStore((state) => state.isUserLoading);
  if (isUserLoading) return <p>Loading user data...</p>;

  const onRowEditInit = (e: any) => setEditingRowId(e.data.VendorId);
  const onRowEditCancel = () => setEditingRowId(null);

  const onRowEditComplete = async (e: any) => {
    try {
      await updateVendor(e.newData.VendorId, { Name: e.newData.Name });
      toastRef.current?.show({
        severity: 'success',
        summary: 'Updated',
        detail: 'Vendor updated',
      });
      mutate();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to update vendor',
      });
    } finally {
      setEditingRowId(null);
    }
  };

  const handleCreate = async () => {
    try {
      const newCreatedVendor = await createVendor(newVendor);
      setNewVendor({ Name: '' });
      toastRef.current?.show({
        severity: 'success',
        summary: 'Created',
        detail: 'Vendor created',
      });
      await mutate();

      // Automatically open the warehouse assignment dialog for the new vendor
      if (newCreatedVendor) {
        setVendorToAssign(newCreatedVendor);
        setAssignWarehousesDialogVisible(true);
        mutateAssignments();
      }
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to create vendor',
      });
    }
  };

  async function handleDelete(v: any) {
    try {
      await updateVendor(v.VendorId, { IsDeleted: true }); // soft-delete
      toastRef.current?.show({
        severity: 'success',
        summary: 'Deleted',
        detail: 'Vendor deleted',
      });
      mutate();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to delete vendor',
      });
    }
  }

  const confirmDelete = (rowData: any) => {
    openDeleteConfirmDialog({ onAccept: () => handleDelete(rowData) });
  };

  const openAssignWarehousesDialog = (rowData: any) => {
    setVendorToAssign(rowData);
    setAssignWarehousesDialogVisible(true);
    mutateAssignments();
  };

  const isWarehouseAssigned = (warehouseId: string) => {
    return assignments?.some(
      (a: any) =>
        a.WarehouseId === warehouseId && a.VendorId === vendorToAssign?.VendorId
    );
  };

  const handleAssignWarehouse = async (warehouseId: string) => {
    if (!vendorToAssign) return;
    try {
      await assignVendorToWarehouse({
        VendorId: vendorToAssign.VendorId,
        WarehouseId: warehouseId,
      });
      mutateAssignments();
      toastRef.current?.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Vendor assigned to warehouse',
      });
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to assign vendor to warehouse',
      });
    }
  };

  const areAllWarehousesAssigned = () => {
    if (!warehouses || !assignments) return false;
    return warehouses.every((warehouse: any) =>
      isWarehouseAssigned(warehouse.WarehouseId)
    );
  };

  const handleAssignOrUnassignAll = async () => {
    if (!vendorToAssign || !warehouses) return;

    const allAssigned = areAllWarehousesAssigned();

    try {
      if (allAssigned) {
        // Unassign from all warehouses
        const unassignmentPromises = warehouses.map((warehouse: any) =>
          removeVendorFromWarehouse({
            VendorId: vendorToAssign.VendorId,
            WarehouseId: warehouse.WarehouseId,
          })
        );

        await Promise.all(unassignmentPromises);

        toastRef.current?.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Vendor unassigned from all warehouses',
        });
      } else {
        // Assign to all unassigned warehouses
        const assignmentPromises = warehouses
          .filter(
            (warehouse: any) => !isWarehouseAssigned(warehouse.WarehouseId)
          )
          .map((warehouse: any) =>
            assignVendorToWarehouse({
              VendorId: vendorToAssign.VendorId,
              WarehouseId: warehouse.WarehouseId,
            })
          );

        await Promise.all(assignmentPromises);

        toastRef.current?.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Vendor assigned to all warehouses',
        });
      }

      mutateAssignments();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: allAssigned
          ? 'Failed to unassign vendor from all warehouses'
          : 'Failed to assign vendor to all warehouses',
      });
    }
  };

  const handleRemoveWarehouse = async (warehouseId: string) => {
    if (!vendorToAssign) return;
    try {
      await removeVendorFromWarehouse({
        VendorId: vendorToAssign.VendorId,
        WarehouseId: warehouseId,
      });
      mutateAssignments();
      toastRef.current?.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Vendor removed from warehouse',
      });
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to remove vendor from warehouse',
      });
    }
  };

  if (!vendors) return <p>Loading...</p>;
  if (error) return <p>Error loading vendors</p>;

  return (
    <>
      <ReusableToast ref={toastRef} />
      <h1>Vendors Management</h1>
      <div className="flex gap-2 my-2">
        <InputText
          placeholder="Name"
          value={newVendor.Name}
          onChange={(e) => setNewVendor({ Name: e.target.value })}
        />
        <Button
          disabled={!newVendor.Name}
          label="Create Vendor"
          onClick={handleCreate}
        />
      </div>

      <DataTable
        value={vendors}
        editMode="row"
        dataKey="VendorId"
        size="small"
        stripedRows
        onRowEditInit={onRowEditInit}
        onRowEditCancel={onRowEditCancel}
        onRowEditComplete={onRowEditComplete}
      >
        {/* <Column field="VendorId" header="ID" /> */}
        <Column
          field="Name"
          header="Name"
          editor={(opts) => textEditor(opts)}
        />
        <Column rowEditor headerStyle={{ width: '4rem' }} />
        <Column
          body={(rowData) => (
            <Button
              icon={<FontAwesomeIcon icon={faTrash} />}
              className="p-button-text p-button-rounded p-button-danger"
              onClick={() => confirmDelete(rowData)}
              tooltip="Delete"
              tooltipOptions={{ position: 'top' }}
            />
          )}
          headerStyle={{ width: '6rem' }}
        />
        <Column
          body={(rowData) => (
            <Button
              label="Warehouses"
              onClick={() => openAssignWarehousesDialog(rowData)}
              className="p-button-sm"
              tooltip="Assign Warehouses"
              tooltipOptions={{ position: 'top' }}
            />
          )}
          headerStyle={{ width: '6rem' }}
          bodyStyle={{ textAlign: 'center' }}
        />
      </DataTable>

      <ReusableConfirmDialog />

      <ReusableDialog
        visible={assignWarehousesDialogVisible}
        onHide={() => setAssignWarehousesDialogVisible(false)}
        header={`Assign Warehouses to ${vendorToAssign?.Name || ''}`}
      >
        {warehouses && assignments ? (
          <div>
            <div className="flex justify-end mb-3">
              <Button
                label={
                  areAllWarehousesAssigned()
                    ? 'Unassign from All Warehouses'
                    : 'Assign to All Warehouses'
                }
                onClick={handleAssignOrUnassignAll}
                className={
                  areAllWarehousesAssigned()
                    ? 'p-button-sm p-button-danger'
                    : 'p-button-sm p-button-success'
                }
              />
            </div>
            {warehouses.map((warehouse: any) => (
              <div
                key={warehouse.WarehouseId}
                className="flex items-center justify-between mb-2"
              >
                <span>{warehouse.Name}</span>
                {isWarehouseAssigned(warehouse.WarehouseId) ? (
                  <Button
                    label="Remove"
                    onClick={() => handleRemoveWarehouse(warehouse.WarehouseId)}
                    className="p-button-secondary"
                  />
                ) : (
                  <Button
                    label="Assign"
                    onClick={() => handleAssignWarehouse(warehouse.WarehouseId)}
                  />
                )}
              </div>
            ))}
          </div>
        ) : (
          <p>Loading warehouses...</p>
        )}
      </ReusableDialog>
    </>
  );
}
