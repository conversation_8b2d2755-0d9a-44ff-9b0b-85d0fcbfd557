'use client';

import useS<PERSON> from 'swr';
import { useRef, useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import ReusableToast from '@/app/shared/components/ReusableToast';
import { But<PERSON> } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { textEditor } from '../../receipts/utils/editors.utils';
import { openDeleteConfirmDialog } from '../../receipts/components/utils/ReceiptsDialogsSettings';
import ReusableConfirmDialog from '@/app/shared/components/ReusableConfirmDialog';
import {
  fetchAssets,
  createAsset,
  updateAsset,
  fetchWarehouses,
  fetchAssetWarehouses,
  assignAssetToWarehouse,
  removeAssetFromWarehouse,
} from '../admin.services';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrash } from '@fortawesome/free-solid-svg-icons';
import ReusableDialog from '@/app/shared/components/ReusableDialog';
import { useAuthStore } from '@/store';

export default function AssetsManagement() {
  const { data: assets, error, mutate } = useSWR('/admin/assets', fetchAssets);
  const [newAsset, setNewAsset] = useState({ Name: '' });
  const [editingRowId, setEditingRowId] = useState<string | null>(null);
  const toastRef = useRef<any>(null);
  const [assignWarehousesDialogVisible, setAssignWarehousesDialogVisible] =
    useState(false);
  const [assetToAssign, setAssetToAssign] = useState<any>(null);

  const isUserLoading = useAuthStore((state) => state.isUserLoading);

  // Fetch warehouses for the dialog
  const { data: warehouses } = useSWR('/admin/warehouses', fetchWarehouses);

  // Fetch asset-warehouse assignments
  const { data: assignments, mutate: mutateAssignments } = useSWR(
    assetToAssign
      ? `/admin/asset-warehouse?assetId=${assetToAssign.AssetId}`
      : null,
    () => fetchAssetWarehouses(assetToAssign?.AssetId)
  );

  const onRowEditInit = (e: any) => setEditingRowId(e.data.AssetId);
  const onRowEditCancel = () => setEditingRowId(null);

  const onRowEditComplete = async (e: any) => {
    try {
      await updateAsset(e.newData.AssetId, { Name: e.newData.Name });
      toastRef.current?.show({
        severity: 'success',
        summary: 'Updated',
        detail: 'Asset updated',
      });
      mutate();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to update asset',
      });
    } finally {
      setEditingRowId(null);
    }
  };

  const handleCreate = async () => {
    try {
      const newCreatedAsset = await createAsset(newAsset);
      setNewAsset({ Name: '' });
      toastRef.current?.show({
        severity: 'success',
        summary: 'Created',
        detail: 'Asset created',
      });
      await mutate();

      // Automatically open the warehouse assignment dialog for the new asset
      if (newCreatedAsset) {
        setAssetToAssign(newCreatedAsset);
        setAssignWarehousesDialogVisible(true);
        mutateAssignments();
      }
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to create asset',
      });
    }
  };

  const handleDelete = async (a: any) => {
    try {
      await updateAsset(a.AssetId, { IsDeleted: true });
      toastRef.current?.show({
        severity: 'success',
        summary: 'Deleted',
        detail: 'Asset deleted',
      });
      mutate();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to delete asset',
      });
    }
  };

  const confirmDelete = (rowData: any) => {
    openDeleteConfirmDialog({ onAccept: () => handleDelete(rowData) });
  };

  const openAssignWarehousesDialog = (rowData: any) => {
    setAssetToAssign(rowData);
    setAssignWarehousesDialogVisible(true);
    mutateAssignments();
  };

  const isWarehouseAssigned = (warehouseId: string) => {
    return assignments?.some(
      (a: any) =>
        a.WarehouseId === warehouseId && a.AssetId === assetToAssign?.AssetId
    );
  };

  const handleAssignWarehouse = async (warehouseId: string) => {
    if (!assetToAssign) return;
    try {
      await assignAssetToWarehouse({
        AssetId: assetToAssign.AssetId,
        WarehouseId: warehouseId,
      });
      mutateAssignments();
      toastRef.current?.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Asset assigned to warehouse',
      });
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to assign asset to warehouse',
      });
    }
  };

  const areAllWarehousesAssigned = () => {
    if (!warehouses || !assignments) return false;
    return warehouses.every((warehouse: any) =>
      isWarehouseAssigned(warehouse.WarehouseId)
    );
  };

  const handleAssignOrUnassignAll = async () => {
    if (!assetToAssign || !warehouses) return;

    const allAssigned = areAllWarehousesAssigned();

    try {
      if (allAssigned) {
        // Unassign from all warehouses
        const unassignmentPromises = warehouses.map((warehouse: any) =>
          removeAssetFromWarehouse({
            AssetId: assetToAssign.AssetId,
            WarehouseId: warehouse.WarehouseId,
          })
        );

        await Promise.all(unassignmentPromises);

        toastRef.current?.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Asset unassigned from all warehouses',
        });
      } else {
        // Assign to all unassigned warehouses
        const assignmentPromises = warehouses
          .filter(
            (warehouse: any) => !isWarehouseAssigned(warehouse.WarehouseId)
          )
          .map((warehouse: any) =>
            assignAssetToWarehouse({
              AssetId: assetToAssign.AssetId,
              WarehouseId: warehouse.WarehouseId,
            })
          );

        await Promise.all(assignmentPromises);

        toastRef.current?.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Asset assigned to all warehouses',
        });
      }

      mutateAssignments();
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: allAssigned
          ? 'Failed to unassign asset from all warehouses'
          : 'Failed to assign asset to all warehouses',
      });
    }
  };

  const handleRemoveWarehouse = async (warehouseId: string) => {
    if (!assetToAssign) return;
    try {
      await removeAssetFromWarehouse({
        AssetId: assetToAssign.AssetId,
        WarehouseId: warehouseId,
      });
      mutateAssignments();
      toastRef.current?.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Asset removed from warehouse',
      });
    } catch (err) {
      toastRef.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to remove asset from warehouse',
      });
    }
  };

  if (!assets) return <p>Loading...</p>;
  if (error) return <p>Error loading assets</p>;

  return (
    <>
      <ReusableToast ref={toastRef} />
      <h1>Assets Management</h1>
      <div className="flex gap-2 my-2">
        <InputText
          placeholder="Name"
          value={newAsset.Name}
          onChange={(e) => setNewAsset({ Name: e.target.value })}
        />
        <Button
          disabled={!newAsset.Name}
          label="Create Asset"
          onClick={handleCreate}
        />
      </div>

      <DataTable
        value={assets}
        editMode="row"
        dataKey="AssetId"
        size="small"
        stripedRows
        onRowEditInit={onRowEditInit}
        onRowEditCancel={onRowEditCancel}
        onRowEditComplete={onRowEditComplete}
      >
        {/* <Column field="AssetId" header="ID" /> */}
        <Column
          field="Name"
          header="Name"
          editor={(opts) => textEditor(opts)}
        />
        <Column rowEditor headerStyle={{ width: '4rem' }} />
        <Column
          body={(rowData) => (
            <Button
              icon={<FontAwesomeIcon icon={faTrash} />}
              className="p-button-text p-button-rounded p-button-danger"
              onClick={() => confirmDelete(rowData)}
              tooltip="Delete"
              tooltipOptions={{ position: 'top' }}
            />
          )}
          headerStyle={{ width: '6rem' }}
        />
        <Column
          body={(rowData) => (
            <Button
              label="Warehouses"
              onClick={() => openAssignWarehousesDialog(rowData)}
              className="p-button-sm"
              tooltip="Assign Warehouses"
              tooltipOptions={{ position: 'top' }}
            />
          )}
          headerStyle={{ width: '6rem' }}
          bodyStyle={{ textAlign: 'center' }}
        />
      </DataTable>

      <ReusableConfirmDialog />

      <ReusableDialog
        visible={assignWarehousesDialogVisible}
        onHide={() => setAssignWarehousesDialogVisible(false)}
        header={`Assign Warehouses to ${assetToAssign?.Name || ''}`}
      >
        {warehouses && assignments ? (
          <div>
            <div className="flex justify-end mb-3">
              <Button
                label={
                  areAllWarehousesAssigned()
                    ? 'Unassign from All Warehouses'
                    : 'Assign to All Warehouses'
                }
                onClick={handleAssignOrUnassignAll}
                className={
                  areAllWarehousesAssigned()
                    ? 'p-button-sm p-button-danger'
                    : 'p-button-sm p-button-success'
                }
              />
            </div>
            {warehouses.map((warehouse: any) => (
              <div
                key={warehouse.WarehouseId}
                className="flex items-center justify-between mb-2"
              >
                <span>{warehouse.Name}</span>
                {isWarehouseAssigned(warehouse.WarehouseId) ? (
                  <Button
                    label="Remove"
                    onClick={() => handleRemoveWarehouse(warehouse.WarehouseId)}
                    className="p-button-secondary"
                  />
                ) : (
                  <Button
                    label="Assign"
                    onClick={() => handleAssignWarehouse(warehouse.WarehouseId)}
                  />
                )}
              </div>
            ))}
          </div>
        ) : (
          <p>Loading warehouses...</p>
        )}
      </ReusableDialog>
    </>
  );
}
