import axios from 'axios';
import { AxiosResponse, AxiosError } from 'axios';
import { getCachedSession } from './sessionCache';

const axiosInstance = axios.create({
  baseURL: '/api',
  headers: { 'Content-Type': 'application/json' },
  timeout: 10000,
});

interface ErrorResponse {
  message?: string;
  error?: string;
}

axiosInstance.interceptors.request.use(async (config) => {
  try {
    // Get session from cache or fetch new one
    const session = await getCachedSession();

    if (session?.accessToken) {
      // Use the access token from the session
      config.headers['Authorization'] = `Bearer ${session.accessToken}`;
    }

    return config;
  } catch (error) {
    console.error('Error setting auth headers:', error);
    return config;
  }
});

axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error: AxiosError<ErrorResponse>) => {
    if (error.response) {
      const errorMessage = error.response.data?.error || error.response.data?.message || 'An error occurred';
      error.message = errorMessage;
      return Promise.reject(error);
    } else if (error.request) {
      return Promise.reject(new Error('No response received from server'));
    } else {
      return Promise.reject(new Error('Request setup error'));
    }
  }
);

export default axiosInstance;
