name: Deploy Docker to <PERSON>'s Azure Container App

on:
  push:
    branches:
      - dev

env:
  ACR_NAME: petersonregistry
  IMAGE_NAME: peterson-receipt-register
  CONTAINER_APP_NAME: peterson-receipt-register
  RESOURCE_GROUP: Lighthouse
  IMAGE_TAG: ${{ github.sha }}

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Log in to Azure
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - name: Azure Container Registry login
        run: az acr login --name $ACR_NAME

      - name: Build and push Docker image to ACR
        run: |
          docker build -t $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG .
          docker push $ACR_NAME.azurecr.io/$IMAGE_NAME:$IMAGE_TAG

      - name: Deploy to Azure Container App
        uses: azure/container-apps-deploy-action@v1
        with:
          acrName: ${{ env.ACR_NAME }}
          containerAppName: ${{ env.CONTAINER_APP_NAME }}
          resourceGroup: ${{ env.RESOURCE_GROUP }}
          imageToDeploy: ${{ env.ACR_NAME }}.azurecr.io/${{ env.IMAGE_NAME }}:${{ env.IMAGE_TAG }}
